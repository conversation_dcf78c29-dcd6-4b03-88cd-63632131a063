import json
from langchain_core.messages import HumanMessage
from langchain_core.prompts import ChatPromptTemplate

from src.graph.state import AgentState, show_agent_reasoning
from pydantic import BaseModel, Field
from typing_extensions import Literal
from src.utils.progress import progress
from src.utils.llm import call_llm
from src.agents.reflection_analyst import load_previous_reflection


class PortfolioDecision(BaseModel):
    action: Literal["buy", "sell", "short", "cover", "hold"]
    quantity: int = Field(description="Number of shares to trade")
    confidence: float = Field(description="Confidence in the decision, between 0.0 and 100.0")
    reasoning: str = Field(description="Reasoning for the decision")


class PortfolioManagerOutput(BaseModel):
    decisions: dict[str, PortfolioDecision] = Field(description="Dictionary of ticker to trading decisions")


##### Portfolio Management Agent #####
def portfolio_management_agent(state: AgentState):
    """Makes final trading decisions and generates orders for multiple tickers"""

    # Get the portfolio and analyst signals
    portfolio = state["data"]["portfolio"]
    analyst_signals = state["data"]["analyst_signals"]
    tickers = state["data"]["tickers"]
    end_date = state["data"]["end_date"]

    # 加载前一日的反思建议（如果存在）
    previous_reflections = load_previous_reflection(end_date, tickers)
    if previous_reflections:
        progress.update_status("portfolio_manager", None, "Loaded previous day reflections")
    else:
        progress.update_status("portfolio_manager", None, "No previous reflections found (first day or no data)")

    # Get position limits, current prices, and signals for every ticker
    position_limits = {}
    current_prices = {}
    max_shares = {}
    signals_by_ticker = {}
    for ticker in tickers:
        progress.update_status("portfolio_manager", ticker, "Processing analyst signals")

        # Get position limits and current prices for the ticker
        risk_data = analyst_signals.get("risk_management_agent", {}).get(ticker, {})
        position_limits[ticker] = risk_data.get("remaining_position_limit", 0)
        current_prices[ticker] = risk_data.get("current_price", 0)

        # Calculate maximum shares allowed based on position limit and price
        if current_prices[ticker] > 0:
            max_shares[ticker] = int(position_limits[ticker] / current_prices[ticker])
        else:
            max_shares[ticker] = 0

        # Get signals for the ticker
        ticker_signals = {}
        for agent, signals in analyst_signals.items():
            if agent != "risk_management_agent" and ticker in signals:
                # Extract basic signal and confidence
                signal_data = {
                    "signal": signals[ticker]["signal"],
                    "confidence": signals[ticker]["confidence"]
                }

                # # Extract reasoning if available and convert to string format
                # if "reasoning" in signals[ticker] and signals[ticker]["reasoning"] is not None:
                #     reasoning = signals[ticker]["reasoning"]
                #     # Handle both string and dict formats, convert to string
                #     if isinstance(reasoning, dict):
                #         signal_data["reasoning"] = str(reasoning)
                #     else:
                #         signal_data["reasoning"] = str(reasoning)
                # else:
                #     # No reasoning available for this agent
                #     signal_data["reasoning"] = None

                ticker_signals[agent] = signal_data
        signals_by_ticker[ticker] = ticker_signals

    progress.update_status("portfolio_manager", None, "Generating trading decisions")

    # Generate the trading decision
    result = generate_trading_decision(
        tickers=tickers,
        signals_by_ticker=signals_by_ticker,
        current_prices=current_prices,
        max_shares=max_shares,
        portfolio=portfolio,
        previous_reflections=previous_reflections,
        model_name=state["metadata"]["model_name"],
        model_provider=state["metadata"]["model_provider"],
    )

    # Create the portfolio management message
    message = HumanMessage(
        content=json.dumps({ticker: decision.model_dump() for ticker, decision in result.decisions.items()}),
        name="portfolio_manager",
    )

    # Print the decision if the flag is set
    if state["metadata"]["show_reasoning"]:
        show_agent_reasoning({ticker: decision.model_dump() for ticker, decision in result.decisions.items()}, "Portfolio Manager")

    progress.update_status("portfolio_manager", None, "Done")

    return {
        "messages": state["messages"] + [message],
        "data": state["data"],
    }


# def generate_trading_decision(
#     tickers: list[str],
#     signals_by_ticker: dict[str, dict],
#     current_prices: dict[str, float],
#     max_shares: dict[str, int],
#     portfolio: dict[str, float],
#     previous_reflections: dict[str, dict],
#     model_name: str,
#     model_provider: str,
# ) -> PortfolioManagerOutput:
#     """Attempts to get a decision from the LLM with retry logic"""
#     # Create the prompt template
#     template = ChatPromptTemplate.from_messages(
#         [
#             (
#                 "system",
#                 """You are an ACTIVE portfolio manager making decisive trading decisions based on multiple analyst signals.

#               CORE PHILOSOPHY: You are NOT a passive observer. Your job is to ACTIVELY trade when signals warrant action.
#               Default to ACTION when you have clear signals, not to holding when uncertain.

#               CRITICAL TRADING RULES - MUST FOLLOW:

#               🚨 POSITION VALIDATION RULES (MANDATORY):
#               - For SELL actions: You MUST currently hold long shares of that ticker (long > 0)
#               - For COVER actions: You MUST currently have short shares of that ticker (short > 0)
#               - For BUY actions: You MUST have sufficient cash available
#               - For SHORT actions: You MUST have sufficient margin available

#               📊 POSITION SIZE LIMITS:
#               - SELL quantity MUST be ≤ current long position shares
#               - COVER quantity MUST be ≤ current short position shares
#               - BUY quantity MUST be ≤ max_shares for that ticker
#               - SHORT quantity MUST respect margin requirements

#               ⚠️  FIRST DAY TRADING LOGIC:
#               - On the first trading day, ALL positions start at 0 (no holdings)
#               - You can ONLY use "buy" or "short" actions on the first day
#               - NEVER use "sell" or "cover" when position = 0
#               - Check current_positions carefully before making decisions

#               - The max_shares values are pre-calculated to respect position limits
#               - Consider both long and short opportunities based on signals
#               - Maintain appropriate risk management with both long and short exposure

#               Available Actions:
#               - "buy": Open or add to long position
#               - "sell": Close or reduce long position
#               - "short": Open or add to short position
#               - "cover": Close or reduce short position
#               - "hold": No action (USE SPARINGLY - only when signals are truly mixed or weak)

#               DECISION FRAMEWORK - FOLLOW THIS PROCESS:

#               1. SIGNAL STRENGTH ASSESSMENT:
#               - HIGH CONVICTION (≥75% confidence): Take FULL position (use max_shares or significant portion)
#               - MEDIUM CONVICTION (50-74% confidence): Take MODERATE position (50-75% of max_shares)
#               - LOW CONVICTION (<50% confidence): Take SMALL position (25-50% of max_shares) or hold
#               - CONFLICTING SIGNALS: Analyze reasoning quality to break ties

#               2. SIGNAL AGGREGATION RULES:
#               - If 60%+ of agents agree on direction (bullish/bearish): STRONG signal - take action
#               - If 40-60% agree: MODERATE signal - consider smaller position
#               - If <40% agree: WEAK signal - hold or very small position
#               - Weight high-confidence agents (>70%) more heavily than low-confidence agents

#               3. REASONING QUALITY WEIGHTING:
#               - Detailed, specific reasoning with data/metrics: HIGH weight
#               - General market commentary without specifics: MEDIUM weight
#               - Vague or generic reasoning: LOW weight
#               - No reasoning (technical/valuation agents): Use confidence score only

#               4. CONFLICT RESOLUTION:
#               - When value investors (Buffett, Graham, Munger) disagree with growth investors (Fisher, Wood, Lynch):
#                 * Consider market conditions and stock characteristics
#                 * Value signals stronger for mature companies, growth signals stronger for innovative companies
#               - When contrarians (Burry) disagree with momentum traders (Druckenmiller):
#                 * Consider recent price action and market sentiment
#                 * Contrarian signals stronger after major moves, momentum signals stronger in trending markets

#               5. ACTION THRESHOLDS:
#               - BUY: Net bullish signal with >55% agent agreement OR high-conviction bullish from 3+ agents
#               - SELL: Net bearish signal with >55% agent agreement OR high-conviction bearish from 3+ agents
#               - SHORT: Strong bearish consensus (>65% agreement) OR very high conviction bearish reasoning
#               - HOLD: Only when signals are genuinely mixed (45-55% split) AND reasoning quality is similar

#               Reflection Learning:
#               - If previous day reflections are provided, carefully consider the insights and recommendations
#               - Learn from past decision quality assessments to improve current decisions
#               - Apply the specific recommendations from the reflection analyst when relevant

#               ACTIVE DECISION FRAMEWORK:
#               - You are an ACTIVE trader, not a passive holder
#               - When multiple agents provide clear signals in the same direction, ACT DECISIVELY
#               - Use the signal strength assessment and aggregation rules defined above
#               - Weight reasoning quality: detailed analysis > general commentary > no reasoning
#               - Break ties using agent expertise: value experts for value stocks, growth experts for growth stocks
#               - Consider market context: contrarian signals in overextended markets, momentum signals in trending markets
#               - Default to ACTION when you have conviction, not to holding when uncertain

#               Inputs:
#               - signals_by_ticker: dictionary of ticker → signals (including reasoning where available)
#               - max_shares: maximum shares allowed per ticker
#               - portfolio_cash: current cash in portfolio
#               - portfolio_positions: current positions (both long and short)
#               - current_prices: current prices for each ticker
#               - margin_requirement: current margin requirement for short positions (e.g., 0.5 means 50%)
#               - total_margin_used: total margin currently in use
#               - previous_reflections: insights and recommendations from previous day's decision analysis (if available)
#               """,
#             ),
#             (
#                 "human",
#                 """MAKE ACTIVE TRADING DECISIONS for each ticker based on the comprehensive team analysis.

#               REMEMBER: You are an ACTIVE portfolio manager. Your goal is to TRADE when signals warrant action.
#               Do NOT default to "hold" unless signals are genuinely mixed or weak.

#               Here are the signals by ticker (including detailed reasoning where available):
#               {signals_by_ticker}

#               DECISION PROCESS - FOLLOW THESE STEPS FOR EACH TICKER:

#               1. 🔍 CHECK CURRENT POSITION FIRST (MANDATORY):
#               - What is the current long position for this ticker? (shares owned)
#               - What is the current short position for this ticker? (shares shorted)
#               - Available cash for new purchases?
#               - Available margin for new shorts?

#               2. 📊 COUNT THE SIGNALS:
#               - How many agents are bullish vs bearish vs neutral?
#               - What's the percentage agreement in each direction?

#               3. 🎯 ASSESS CONFIDENCE LEVELS:
#               - Which agents have high confidence (>70%)?
#               - Which agents have detailed, specific reasoning?

#               4. ⚖️  VALIDATE ACTION FEASIBILITY:
#               - If considering SELL: Do I have long shares to sell? (long > 0)
#               - If considering COVER: Do I have short shares to cover? (short > 0)
#               - If considering BUY: Do I have sufficient cash?
#               - If considering SHORT: Do I have sufficient margin?

#               5. 📈 APPLY DECISION RULES:
#               - 60%+ agreement + high confidence = STRONG signal → Take FULL position
#               - 40-60% agreement = MODERATE signal → Take PARTIAL position
#               - <40% agreement = WEAK signal → Small position or hold
#               - High-conviction minority (3+ agents >75% confidence) can override weak majority

#               6. 📏 DETERMINE POSITION SIZE:
#               - High conviction: Use 75-100% of max_shares
#               - Medium conviction: Use 50-75% of max_shares
#               - Low conviction: Use 25-50% of max_shares

#               FINAL DECISION GUIDELINES:

#               🚨 POSITION VALIDATION (CRITICAL):
#               - ALWAYS check current positions before deciding action
#               - NEVER sell when long position = 0
#               - NEVER cover when short position = 0
#               - ALWAYS verify you have sufficient cash/margin for new positions

#               📈 DECISION QUALITY:
#               - BE DECISIVE: When you have clear signals (>55% agreement), take action
#               - BE BOLD: When you have high conviction (multiple agents >75% confidence), use significant position sizes
#               - BE SMART: Weight reasoning quality - detailed analysis beats generic commentary
#               - BE ADAPTIVE: Consider market context when resolving conflicts between different investment styles
#               - BE ACTIVE: Your job is to trade profitably, not to avoid all risk

#               EXAMPLES OF WHEN TO ACT:
#               ✅ 4+ agents bullish + have cash → BUY (significant position)
#               ✅ 3+ agents bearish + have long shares → SELL
#               ✅ 3+ agents bearish + have margin → SHORT
#               ✅ Mixed signals but 2+ high-conviction (>80%) agents agree + position allows → Follow high-conviction minority
#               ✅ Value agents bullish on undervalued stock + have cash → BUY
#               ✅ Growth agents bearish on overvalued growth stock + have long shares → SELL

#               EXAMPLES OF WHEN TO HOLD:
#               ❌ Signals split 50/50 with similar confidence levels
#               ❌ All agents have low confidence (<50%)
#               ❌ Reasoning is vague across all agents
#               ❌ Want to SELL but long position = 0 (INVALID ACTION)
#               ❌ Want to COVER but short position = 0 (INVALID ACTION)
#               ❌ Want to BUY but insufficient cash
#               ❌ Want to SHORT but insufficient margin

#               Current Prices:
#               {current_prices}

#               Maximum Shares Allowed For Purchases:
#               {max_shares}

#               Portfolio Cash: {portfolio_cash}
#               Current Positions: {portfolio_positions}
#               Current Margin Requirement: {margin_requirement}
#               Total Margin Used: {total_margin_used}

#               Previous Day Reflections (learn from these insights):
#               {previous_reflections}

#               Output strictly in JSON with the following structure:
#               {{
#                 "decisions": {{
#                   "TICKER1": {{
#                     "action": "buy/sell/short/cover/hold",
#                     "quantity": integer,
#                     "confidence": float between 0 and 100,
#                     "reasoning": "string"
#                   }},
#                   "TICKER2": {{
#                     ...
#                   }},
#                   ...
#                 }}
#               }}
#               """,
#             ),
#         ]
#     )

#     # Generate the prompt
#     prompt = template.invoke(
#         {
#             "signals_by_ticker": json.dumps(signals_by_ticker, indent=2),
#             "current_prices": json.dumps(current_prices, indent=2),
#             "max_shares": json.dumps(max_shares, indent=2),
#             "portfolio_cash": f"{portfolio.get('cash', 0):.2f}",
#             "portfolio_positions": json.dumps(portfolio.get("positions", {}), indent=2),
#             "margin_requirement": f"{portfolio.get('margin_requirement', 0):.2f}",
#             "total_margin_used": f"{portfolio.get('margin_used', 0):.2f}",
#             "previous_reflections": json.dumps(previous_reflections, indent=2, ensure_ascii=False) if previous_reflections else "无前一日反思数据（首日交易或数据不可用）",
#         }
#     )

#     # Create default factory for PortfolioManagerOutput
#     def create_default_portfolio_output():
#         return PortfolioManagerOutput(decisions={ticker: PortfolioDecision(action="hold", quantity=0, confidence=0.0, reasoning="Error in portfolio management, defaulting to hold") for ticker in tickers})

#     return call_llm(prompt=prompt, model_name=model_name, model_provider=model_provider, pydantic_model=PortfolioManagerOutput, agent_name="portfolio_manager", default_factory=create_default_portfolio_output)


def generate_trading_decision(
    tickers: list[str],
    signals_by_ticker: dict[str, dict],
    current_prices: dict[str, float],
    max_shares: dict[str, int],
    portfolio: dict[str, float],
    previous_reflections: dict[str, dict],
    model_name: str,
    model_provider: str,
) -> PortfolioManagerOutput:
    """Attempts to get a decision from the LLM with retry logic"""
    # Create the prompt template
    template = ChatPromptTemplate.from_messages(
        [
            (
                "system",
                """You are a portfolio manager making final trading decisions based on multiple tickers.

              Trading Rules:
              - For long positions:
                * Only buy if you have available cash
                * Only sell if you currently hold long shares of that ticker
                * Sell quantity must be ≤ current long position shares
                * Buy quantity must be ≤ max_shares for that ticker
              
              - For short positions:
                * Only short if you have available margin (position value × margin requirement)
                * Only cover if you currently have short shares of that ticker
                * Cover quantity must be ≤ current short position shares
                * Short quantity must respect margin requirements
              
              - The max_shares values are pre-calculated to respect position limits
              - Consider both long and short opportunities based on signals
              - Maintain appropriate risk management with both long and short exposure

              Available Actions:
              - "buy": Open or add to long position
              - "sell": Close or reduce long position
              - "short": Open or add to short position
              - "cover": Close or reduce short position
              - "hold": No action

              Inputs:
              - signals_by_ticker: dictionary of ticker → signals
              - max_shares: maximum shares allowed per ticker
              - portfolio_cash: current cash in portfolio
              - portfolio_positions: current positions (both long and short)
              - current_prices: current prices for each ticker
              - margin_requirement: current margin requirement for short positions (e.g., 0.5 means 50%)
              - total_margin_used: total margin currently in use
              """,
            ),
            (
                "human",
                """Based on the team's analysis, make your trading decisions for each ticker.

              Here are the signals by ticker:
              {signals_by_ticker}

              Current Prices:
              {current_prices}

              Maximum Shares Allowed For Purchases:
              {max_shares}

              Portfolio Cash: {portfolio_cash}
              Current Positions: {portfolio_positions}
              Current Margin Requirement: {margin_requirement}
              Total Margin Used: {total_margin_used}
            
              Previous Day Reflections (learn from these insights):
              {previous_reflections}

              Output strictly in JSON with the following structure:
              {{
                "decisions": {{
                  "TICKER1": {{
                    "action": "buy/sell/short/cover/hold",
                    "quantity": integer,
                    "confidence": float between 0 and 100,
                    "reasoning": "string"
                  }},
                  "TICKER2": {{
                    ...
                  }},
                  ...
                }}
              }}
              """,
            ),
        ]
    )

    # Generate the prompt
    prompt = template.invoke(
        {
            "signals_by_ticker": json.dumps(signals_by_ticker, indent=2),
            "current_prices": json.dumps(current_prices, indent=2),
            "max_shares": json.dumps(max_shares, indent=2),
            "portfolio_cash": f"{portfolio.get('cash', 0):.2f}",
            "portfolio_positions": json.dumps(portfolio.get("positions", {}), indent=2),
            "margin_requirement": f"{portfolio.get('margin_requirement', 0):.2f}",
            "total_margin_used": f"{portfolio.get('margin_used', 0):.2f}",
        }
    )

    # Create default factory for PortfolioManagerOutput
    def create_default_portfolio_output():
        return PortfolioManagerOutput(decisions={ticker: PortfolioDecision(action="hold", quantity=0, confidence=0.0, reasoning="Error in portfolio management, defaulting to hold") for ticker in tickers})

    return call_llm(prompt=prompt, model_name=model_name, model_provider=model_provider, pydantic_model=PortfolioManagerOutput, agent_name="portfolio_manager", default_factory=create_default_portfolio_output)