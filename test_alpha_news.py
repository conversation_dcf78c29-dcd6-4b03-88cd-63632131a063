import requests
import json
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv

# ✅ 加载 .env 文件中的环境变量
load_dotenv()
API_KEY = os.getenv("ALPHA_VANTAGE_API_KEY")

if not API_KEY:
    raise ValueError("❌ 未能在 .env 文件中找到 'ALPHA_VANTAGE_API_KEY'，请检查设置。")

# ✅ 构建 API 请求参数
base_url = "https://www.alphavantage.co/query"
params = {
    "function": "NEWS_SENTIMENT",
    "tickers": "AAPL",
    "topics": "technology",
    "time_from": "20250201T0000",  # 指定开始日期时间，格式为：YYYYMMDDTHHMMM（年月日T时分）
    "time_to": "20250331T2359",    # 可选：指定结束日期时间
    "sort": "LATEST",
    "limit": 1000,
    "apikey": API_KEY
}

# ✅ 创建保存文件的目录
output_dir = "alpha_news"
os.makedirs(output_dir, exist_ok=True)

# ✅ 请求数据
response = requests.get(base_url, params=params)

if response.status_code == 200:
    data = response.json()
    news_items = data.get("feed", [])

    print(f"共获取到 {len(news_items)} 条新闻，开始分类保存...\n")

    for item in news_items:
        time_published = item.get("time_published")
        if not time_published:
            continue
        date_str = datetime.strptime(time_published[:8], "%Y%m%d").strftime("%Y-%m-%d")
        file_path = os.path.join(output_dir, f"{date_str}.jsonl")

        with open(file_path, "a", encoding="utf-8") as f:
            f.write(json.dumps(item, ensure_ascii=False) + "\n")

    print(f"✅ 新闻已按日期分类保存至目录：{output_dir}")
else:
    print("❌ 请求失败，状态码：", response.status_code)
    print(response.text)
